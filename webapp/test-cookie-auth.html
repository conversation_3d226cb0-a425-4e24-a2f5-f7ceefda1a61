<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cookie Authentication Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="email"], input[type="password"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .cookie-info {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <h1>🍪 Cookie Authentication Test</h1>
    
    <div class="cookie-info">
        <strong>Note:</strong> This test page verifies that HTTP-only cookies are working for cross-subdomain authentication.
        Make sure your server is running on the correct domain with HTTPS enabled for production testing.
    </div>

    <div class="container">
        <h2>1. Login Test</h2>
        <div class="form-group">
            <label for="email">Email:</label>
            <input type="email" id="email" placeholder="Enter your email">
        </div>
        <div class="form-group">
            <label for="password">Password:</label>
            <input type="password" id="password" placeholder="Enter your password">
        </div>
        <button onclick="testLogin()">Login</button>
        <div id="loginResult" class="result" style="display: none;"></div>
    </div>

    <div class="container">
        <h2>2. Check Authentication Status</h2>
        <p>This will test if the HTTP-only cookie is being sent and validated by the server.</p>
        <button onclick="checkAuth()">Check Auth Status</button>
        <div id="authResult" class="result" style="display: none;"></div>
    </div>

    <div class="container">
        <h2>3. Logout Test</h2>
        <p>This will clear the HTTP-only cookies on the server.</p>
        <button onclick="testLogout()">Logout</button>
        <div id="logoutResult" class="result" style="display: none;"></div>
    </div>

    <div class="container">
        <h2>4. Cookie Information</h2>
        <button onclick="showCookieInfo()">Show Browser Cookies</button>
        <div id="cookieInfo" class="result" style="display: none;"></div>
    </div>

    <script>
        // Configuration
        const API_URL = 'http://localhost:3002/api'; // Adjust this to your server URL

        // Helper function to display results
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `result ${type}`;
            element.style.display = 'block';
        }

        // Test login functionality
        async function testLogin() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;

            if (!email || !password) {
                showResult('loginResult', 'Please enter both email and password', 'error');
                return;
            }

            try {
                const response = await fetch(`${API_URL}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    credentials: 'include', // Important: This sends cookies
                    body: JSON.stringify({ email, password })
                });

                const data = await response.json();

                if (response.ok) {
                    showResult('loginResult', `✅ Login successful!\n\nResponse:\n${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    showResult('loginResult', `❌ Login failed:\n${data.error || 'Unknown error'}`, 'error');
                }
            } catch (error) {
                showResult('loginResult', `❌ Network error:\n${error.message}`, 'error');
            }
        }

        // Check authentication status
        async function checkAuth() {
            try {
                const response = await fetch(`${API_URL}/auth/check-auth`, {
                    method: 'GET',
                    credentials: 'include', // Important: This sends cookies
                });

                const data = await response.json();

                if (response.ok) {
                    showResult('authResult', `✅ Authentication valid!\n\nUser Info:\n${JSON.stringify(data.user, null, 2)}`, 'success');
                } else {
                    showResult('authResult', `❌ Authentication failed:\n${data.error || 'Not authenticated'}`, 'error');
                }
            } catch (error) {
                showResult('authResult', `❌ Network error:\n${error.message}`, 'error');
            }
        }

        // Test logout functionality
        async function testLogout() {
            try {
                const response = await fetch(`${API_URL}/auth/logout`, {
                    method: 'POST',
                    credentials: 'include', // Important: This sends cookies
                });

                const data = await response.json();

                if (response.ok) {
                    showResult('logoutResult', `✅ Logout successful!\n\nResponse:\n${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    showResult('logoutResult', `❌ Logout failed:\n${data.error || 'Unknown error'}`, 'error');
                }
            } catch (error) {
                showResult('logoutResult', `❌ Network error:\n${error.message}`, 'error');
            }
        }

        // Show cookie information
        function showCookieInfo() {
            const cookies = document.cookie;
            const cookieArray = cookies.split(';').map(cookie => cookie.trim()).filter(cookie => cookie);
            
            let cookieInfo = `Browser Cookies (JavaScript accessible only):\n\n`;
            
            if (cookieArray.length === 0) {
                cookieInfo += 'No JavaScript-accessible cookies found.\n\n';
            } else {
                cookieArray.forEach(cookie => {
                    cookieInfo += `${cookie}\n`;
                });
                cookieInfo += '\n';
            }
            
            cookieInfo += `Note: HTTP-only cookies (like auth_token and refresh_token) are not visible to JavaScript for security reasons. They are automatically sent with requests when credentials: 'include' is used.`;
            
            showResult('cookieInfo', cookieInfo, 'info');
        }

        // Auto-check auth status on page load
        window.addEventListener('load', () => {
            checkAuth();
        });
    </script>
</body>
</html>

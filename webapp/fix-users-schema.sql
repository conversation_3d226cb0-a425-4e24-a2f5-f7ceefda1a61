-- Fix Users Table Schema
-- This script adds missing columns to the users table to match the User model

-- Set the search path to the site schema
SET search_path TO site;

-- Start transaction
BEGIN;

-- Add phone verification columns if they don't exist
ALTER TABLE users ADD COLUMN IF NOT EXISTS phone_verified BOOLEAN DEFAULT FALSE;
COMMENT ON COLUMN users.phone_verified IS 'Whether the user has verified their phone number';

ALTER TABLE users ADD COLUMN IF NOT EXISTS phone_verification_code VARCHAR(10);
COMMENT ON COLUMN users.phone_verification_code IS 'Temporary code for phone verification';

ALTER TABLE users ADD COLUMN IF NOT EXISTS phone_verification_expires TIMESTAMP;
COMMENT ON COLUMN users.phone_verification_expires IS 'Expiration time for phone verification code';

-- Add email verification columns if they don't exist
ALTER TABLE users ADD COLUMN IF NOT EXISTS email_verification_token VARCHAR(255);
COMMENT ON COLUMN users.email_verification_token IS 'Token for email verification';

ALTER TABLE users ADD COLUMN IF NOT EXISTS email_verification_expires TIMESTAMP;
COMMENT ON COLUMN users.email_verification_expires IS 'Expiration time for email verification token';

-- Add 2FA email columns if they don't exist
ALTER TABLE users ADD COLUMN IF NOT EXISTS email_2fa_code VARCHAR(255);
COMMENT ON COLUMN users.email_2fa_code IS 'Verification code for email-based 2FA';

ALTER TABLE users ADD COLUMN IF NOT EXISTS email_2fa_expires TIMESTAMP;
COMMENT ON COLUMN users.email_2fa_expires IS 'Expiration timestamp for email-based 2FA code';

-- Add matrix token column if it doesn't exist
ALTER TABLE users ADD COLUMN IF NOT EXISTS matrix_token VARCHAR(255);
COMMENT ON COLUMN users.matrix_token IS 'Token used for authenticating with the Matrix chat system';

-- Add stripe customer ID column if it doesn't exist
ALTER TABLE users ADD COLUMN IF NOT EXISTS stripe_customer_id VARCHAR(255);
COMMENT ON COLUMN users.stripe_customer_id IS 'Stripe customer ID for this user';

-- Add user type and business columns if they don't exist
ALTER TABLE users ADD COLUMN IF NOT EXISTS user_type VARCHAR(50) DEFAULT 'farmer';
COMMENT ON COLUMN users.user_type IS 'Type of user account';

ALTER TABLE users ADD COLUMN IF NOT EXISTS is_business_owner BOOLEAN DEFAULT FALSE;
COMMENT ON COLUMN users.is_business_owner IS 'Whether the user owns a business listing (supplier or vet)';

ALTER TABLE users ADD COLUMN IF NOT EXISTS is_approved BOOLEAN DEFAULT FALSE;
COMMENT ON COLUMN users.is_approved IS 'Whether the business account has been approved by an admin';

-- Add help tips column if it doesn't exist
ALTER TABLE users ADD COLUMN IF NOT EXISTS help_tips_disabled BOOLEAN DEFAULT FALSE;
COMMENT ON COLUMN users.help_tips_disabled IS 'Whether the user has disabled all help tips';

-- Add subscription plan column if it doesn't exist
ALTER TABLE users ADD COLUMN IF NOT EXISTS subscription_plan_id UUID;
COMMENT ON COLUMN users.subscription_plan_id IS 'Subscription plan for business owners';

-- Add constraint for user_type if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.check_constraints 
        WHERE constraint_name = 'check_user_type' 
        AND table_name = 'users'
        AND table_schema = 'site'
    ) THEN
        ALTER TABLE users ADD CONSTRAINT check_user_type 
            CHECK (user_type IN ('farmer', 'supplier', 'vet', 'admin', 'accountant'));
    END IF;
END$$;

-- Create indexes for better performance if they don't exist
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_user_type ON users(user_type);
CREATE INDEX IF NOT EXISTS idx_users_is_business_owner ON users(is_business_owner);
CREATE INDEX IF NOT EXISTS idx_users_is_approved ON users(is_approved);
CREATE INDEX IF NOT EXISTS idx_users_subscription_plan_id ON users(subscription_plan_id);
CREATE INDEX IF NOT EXISTS idx_users_email_verified ON users(email_verified);
CREATE INDEX IF NOT EXISTS idx_users_phone_verified ON users(phone_verified);

-- Commit the transaction
COMMIT;

-- Display the updated table structure
\d users;

-- Show a summary of what was done
SELECT 'Users table schema has been updated successfully!' as status;

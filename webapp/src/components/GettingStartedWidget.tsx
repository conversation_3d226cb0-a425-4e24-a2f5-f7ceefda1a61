import React, { useState, useEffect } from 'react';
import { useFarm } from '../context/FarmContext';
import { useAuth } from '../context/AuthContext';
import axios from 'axios';
import { API_URL } from '../config';

interface SetupStep {
  id: string;
  title: string;
  description: string;
  completed: boolean;
  link: string;
}

const GettingStartedWidget: React.FC = () => {
  const { currentFarm } = useFarm();
  const { user, token } = useAuth();
  const [loading, setLoading] = useState(true);
  const [setupSteps, setSetupSteps] = useState<SetupStep[]>([]);
  const [progress, setProgress] = useState(0);
  const [isExistingUser, setIsExistingUser] = useState(false);

  useEffect(() => {
    const fetchSetupProgress = async () => {
      if (!currentFarm || !user || !token) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);

        // Initialize setup steps
        const steps: SetupStep[] = [
          {
            id: 'farm-setup',
            title: 'Set up your farm',
            description: 'Complete your farm profile with basic information.',
            completed: !!currentFarm,
            link: '/farms'
          },
          {
            id: 'add-customers',
            title: 'Add customers',
            description: 'Add your customers to start sending invoices.',
            completed: false,
            link: '/customers'
          },
          {
            id: 'add-employees',
            title: 'Add employees',
            description: 'Add employees to your farm and assign roles.',
            completed: false,
            link: '/employees'
          },
          {
            id: 'add-fields',
            title: 'Add fields',
            description: 'Add and configure your fields.',
            completed: false,
            link: '/fields'
          },
          {
            id: 'send-invoice',
            title: 'Send first invoice',
            description: 'Create and send your first invoice to a customer.',
            completed: false,
            link: '/invoices/new'
          },
          {
            id: 'link-account',
            title: 'Link financial account',
            description: 'Connect your bank account or accounting software.',
            completed: false,
            link: '/finances/connect'
          },
          {
            id: 'explore-help',
            title: 'Explore help resources',
            description: 'Check out our help documentation, support, and FAQ.',
            completed: false,
            link: '/help'
          }
        ];

        // Check if farm has customers
        try {
          const customersResponse = await axios.get(`${API_URL}/farms/${currentFarm.id}/customers`, {
            headers: { Authorization: `Bearer ${token}` }
          });

          // If there are customers, mark as completed
          if (customersResponse.data && 
              (customersResponse.data.customers || customersResponse.data) && 
              Array.isArray(customersResponse.data.customers || customersResponse.data) && 
              (customersResponse.data.customers || customersResponse.data).length > 0) {
            steps[1].completed = true;
          }
        } catch (error) {
          console.error('Error checking customers:', error);
        }

        // Check if farm has employees
        try {
          const employeesResponse = await axios.get(`${API_URL}/farms/${currentFarm.id}/users`, {
            headers: { Authorization: `Bearer ${token}` }
          });

          // If there are employees other than the current user, mark as completed
          if (employeesResponse.data && 
              Array.isArray(employeesResponse.data) && 
              employeesResponse.data.length > 1) {
            steps[2].completed = true;
          }
        } catch (error) {
          console.error('Error checking employees:', error);
        }

        // Check if farm has fields
        try {
          const fieldsResponse = await axios.get(`${API_URL}/fields?farmId=${currentFarm.id}`, {
            headers: { Authorization: `Bearer ${token}` }
          });

          // If there are fields, mark as completed
          if (fieldsResponse.data && 
              Array.isArray(fieldsResponse.data) && 
              fieldsResponse.data.length > 0) {
            steps[3].completed = true;
          }
        } catch (error) {
          console.error('Error checking fields:', error);
        }

        // Check if farm has sent any invoices
        try {
          const invoicesResponse = await axios.get(`${API_URL}/farms/${currentFarm.id}/invoices`, {
            headers: { Authorization: `Bearer ${token}` }
          });

          // If there are invoices, mark as completed
          if (invoicesResponse.data && 
              (invoicesResponse.data.invoices || invoicesResponse.data) && 
              Array.isArray(invoicesResponse.data.invoices || invoicesResponse.data) && 
              (invoicesResponse.data.invoices || invoicesResponse.data).length > 0) {
            steps[4].completed = true;
          }
        } catch (error) {
          console.error('Error checking invoices:', error);
        }

        // Check if farm has linked accounts
        try {
          const accountsResponse = await axios.get(`${API_URL}/financial-connections/farm/${currentFarm.id}`, {
            headers: { Authorization: `Bearer ${token}` }
          });

          // If there are linked accounts, mark as completed
          if (accountsResponse.data && 
              Array.isArray(accountsResponse.data) && 
              accountsResponse.data.length > 0) {
            steps[5].completed = true;
          }
        } catch (error) {
          console.error('Error checking linked accounts:', error);
        }

        // Calculate progress
        const completedSteps = steps.filter(step => step.completed).length;
        const progressPercentage = Math.round((completedSteps / steps.length) * 100);

        // Determine if user is an existing user based on progress
        // If they've completed at least 3 steps or have more than 50% progress, consider them an existing user
        const existingUser = completedSteps >= 3 || progressPercentage >= 50;
        setIsExistingUser(existingUser);

        // If user is an existing user, add different tasks
        if (existingUser) {
          // Replace the steps with tasks for existing users
          steps = [
            {
              id: 'explore-new-features',
              title: 'Explore new features',
              description: 'Check out the latest features and improvements.',
              completed: false,
              link: '/whats-new'
            },
            {
              id: 'setup-integrations',
              title: 'Set up integrations',
              description: 'Connect with other tools and services you use.',
              completed: false,
              link: '/integrations'
            },
            {
              id: 'configure-notifications',
              title: 'Configure notifications',
              description: 'Customize how and when you receive alerts.',
              completed: false,
              link: '/settings/notifications'
            },
            {
              id: 'review-account',
              title: 'Review account settings',
              description: 'Make sure your account information is up to date.',
              completed: false,
              link: '/settings/account'
            },
            {
              id: 'invite-team',
              title: 'Invite team members',
              description: 'Add more users to collaborate with you.',
              completed: false,
              link: '/team/invite'
            }
          ];
        }

        setSetupSteps(steps);
        setProgress(progressPercentage);
        setLoading(false);
      } catch (error) {
        console.error('Error fetching setup progress:', error);
        setLoading(false);
      }
    };

    fetchSetupProgress();
  }, [currentFarm, user, token]);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-40">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
        <span className="ml-2 text-gray-500">Loading setup progress...</span>
      </div>
    );
  }

  return (
    <div className="p-4">
      <div className="mb-4">
        <h2 className="text-lg font-medium text-gray-900 mb-1">
          {isExistingUser ? 'Next Steps' : 'Getting Started'}
        </h2>
        <p className="text-sm text-gray-500">
          {isExistingUser 
            ? 'Here are some recommended actions to get the most out of your farm management system.'
            : 'Complete these steps to set up your farm management system.'}
        </p>
      </div>

      {/* Progress bar */}
      <div className="w-full bg-gray-200 rounded-full h-2.5 mb-4">
        <div 
          className="bg-primary-600 h-2.5 rounded-full" 
          style={{ width: `${progress}%` }}
        ></div>
      </div>
      <p className="text-sm text-gray-500 mb-4">{progress}% complete</p>

      {/* Setup steps */}
      <div className="space-y-4">
        {setupSteps.map((step) => (
          <div 
            key={step.id}
            className={`p-3 border rounded-lg ${
              step.completed ? 'border-green-200 bg-green-50' : 'border-gray-200 bg-white'
            }`}
          >
            <div className="flex items-start">
              <div className={`flex-shrink-0 w-5 h-5 rounded-full flex items-center justify-center mt-0.5 ${
                step.completed ? 'bg-green-500' : 'bg-gray-200'
              }`}>
                {step.completed && (
                  <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                )}
              </div>
              <div className="ml-3 flex-1">
                <h3 className={`text-sm font-medium ${
                  step.completed ? 'text-green-800' : 'text-gray-900'
                }`}>
                  {step.title}
                </h3>
                <p className="mt-1 text-xs text-gray-500">{step.description}</p>
                {!step.completed && (
                  <a 
                    href={step.link} 
                    className="mt-2 inline-block text-xs font-medium text-primary-600 hover:text-primary-800"
                  >
                    {isExistingUser ? 'Explore this option →' : 'Complete this step →'}
                  </a>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Help and support section */}
      <div className="mt-6 border-t border-gray-200 pt-4">
        <h3 className="text-sm font-medium text-gray-900 mb-2">Need help?</h3>
        <div className="grid grid-cols-3 gap-2">
          <a 
            href="/help" 
            className="text-xs text-center py-2 px-3 border border-gray-200 rounded-md hover:bg-gray-50"
          >
            Help Center
          </a>
          <a 
            href="/support" 
            className="text-xs text-center py-2 px-3 border border-gray-200 rounded-md hover:bg-gray-50"
          >
            Contact Support
          </a>
          <a 
            href="/faq" 
            className="text-xs text-center py-2 px-3 border border-gray-200 rounded-md hover:bg-gray-50"
          >
            FAQ
          </a>
        </div>
      </div>
    </div>
  );
};

export default GettingStartedWidget;

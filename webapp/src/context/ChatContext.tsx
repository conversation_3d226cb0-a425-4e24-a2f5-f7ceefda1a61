import React, { createContext, useContext, useState, useEffect, useCallback, useRef } from 'react';
import { useAuth } from './AuthContext';
import { useNotification } from './NotificationContext';
import axios from 'axios';
import { sendUnreadMessagesEmail, createUnreadMessagesAlertRule } from '../services/chatNotificationService';
import { MatrixClient, createClient, Room, RoomEvent, MatrixEvent } from 'matrix-js-sdk';
import { MATRIX_SERVER_URL, MATRIX_DOMAIN } from '../config';

// Types
export interface User {
  id: string;
  first_name: string;
  last_name: string;
  firstName: string;
  lastName: string;
  email: string;
  is_online?: boolean;
  last_active_at?: string;
  matrix_token?: string;
  farm_id?: string;
  currentFarmId?: string;
}

export interface Message {
  id: string;
  conversation_id: string;
  sender_id: string;
  message_type: 'text' | 'image' | 'file' | 'system' | 'task';
  content: string;
  is_edited: boolean;
  parent_message_id?: string;
  created_at: string;
  updated_at: string;
  sender_first_name?: string;
  sender_last_name?: string;
  sender_email?: string;
  attachments?: Attachment[];
  reactions?: Reaction[];
  read_status?: ReadStatus[];
}

export interface Attachment {
  id: string;
  message_id: string;
  file_name: string;
  file_type: string;
  file_size: number;
  file_url: string;
  thumbnail_url?: string;
  created_at: string;
  updated_at: string;
}

export interface Reaction {
  id: string;
  message_id: string;
  user_id: string;
  reaction: string;
  created_at: string;
  updated_at: string;
  first_name?: string;
  last_name?: string;
  email?: string;
}

export interface ReadStatus {
  id: string;
  message_id: string;
  user_id: string;
  is_read: boolean;
  read_at?: string;
  created_at: string;
  updated_at: string;
  first_name?: string;
  last_name?: string;
  email?: string;
}

export interface Conversation {
  id: string;
  name?: string;
  type: 'direct' | 'group' | 'channel';
  farm_id?: string;
  created_by: string;
  is_pinned: boolean;
  created_at: string;
  updated_at: string;
  participants?: User[];
  unread_count?: number;
}

// Typing user interface
interface TypingUser {
  id: string;
  is_typing: boolean;
}

// Context type
interface ChatContextType {
  conversations: Conversation[];
  activeConversation: Conversation | null;
  messages: Message[];
  isLoading: boolean;
  error: string | null;
  loadConversations: () => Promise<void>;
  loadMessages: (conversationId: string) => Promise<void>;
  sendMessage: (conversationId: string, content: string, messageType?: string) => Promise<any>;
  createConversation: (participants: string[], name?: string, type?: 'direct' | 'group' | 'channel') => Promise<Conversation | null>;
  markAsRead: (conversationId: string, messageIds: string[]) => Promise<void>;
  addReaction: (conversationId: string, messageId: string, reaction: string) => Promise<void>;
  uploadAttachment: (conversationId: string, file: File) => Promise<Attachment>;
  setActiveConversation: (conversation: Conversation | null) => void;
  isConnected: boolean;
  typingUsers: TypingUser[];
  onlineUsers: Record<string, boolean>;
  sendTypingIndicator: (conversationId: string, isTyping: boolean) => void;
}

// Create context with default values
const ChatContext = createContext<ChatContextType>({
  conversations: [],
  activeConversation: null,
  messages: [],
  isLoading: false,
  error: null,
  loadConversations: async () => {},
  loadMessages: async () => {},
  sendMessage: async () => {},
  createConversation: async () => null,
  markAsRead: async () => {},
  addReaction: async () => {},
  uploadAttachment: async () => ({} as Attachment),
  setActiveConversation: () => {},
  isConnected: false,
  typingUsers: [],
  onlineUsers: {},
  sendTypingIndicator: () => {},
});

// WebSocket message types
interface WebSocketMessage {
  type: string;
  [key: string]: any;
}

// Provider component
export const ChatProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user, getToken } = useAuth();
  const { showNotification } = useNotification();
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [activeConversation, setActiveConversation] = useState<Conversation | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [isConnected, setIsConnected] = useState<boolean>(false);
  const [notificationRuleSetup, setNotificationRuleSetup] = useState<boolean>(false);
  const [typingUsers, setTypingUsers] = useState<TypingUser[]>([]);
  const [onlineUsers, setOnlineUsers] = useState<Record<string, boolean>>({});

  // Matrix client reference
  const matrixClient = useRef<MatrixClient | null>(null);

  // Typing timeouts reference
  const typingTimeoutsRef = useRef<Record<string, NodeJS.Timeout>>({});

  // Connect to Matrix client
  const connectMatrixClient = useCallback(async () => {
    if (!user) return;

    try {
      // Get the user's matrix token
      const userWithMatrix = user as any;
      if (!userWithMatrix.matrix_token) {
        console.error('User does not have a matrix token');
        return;
      }

      const token = userWithMatrix.matrix_token;
      const deviceId = localStorage.getItem(`matrix_device_id_${user.id}`) || `DEVICE_${user.id}_${Date.now()}`;

      // Store device ID for future use
      if (!localStorage.getItem(`matrix_device_id_${user.id}`)) {
        localStorage.setItem(`matrix_device_id_${user.id}`, deviceId);
      }

      // Create Matrix client with encryption enabled
      matrixClient.current = createClient({
        baseUrl: MATRIX_SERVER_URL,
        accessToken: token,
        userId: `@${user.id}:${MATRIX_DOMAIN}`,
        deviceId,
        // Use default crypto store configuration
        cryptoStore: window.indexedDB ? undefined : undefined,
      });

      // Set up event listeners
      matrixClient.current.on('sync' as any, (state: string) => {
        switch (state) {
          case 'PREPARED':
            console.log('Matrix client connected and synced');
            setIsConnected(true);
            break;
          case 'STOPPED':
            console.log('Matrix client stopped');
            setIsConnected(false);
            break;
          case 'ERROR':
            console.error('Matrix client sync error');
            setIsConnected(false);
            // Attempt to reconnect after a delay
            setTimeout(() => {
              if (matrixClient.current) {
                matrixClient.current.startClient();
              } else {
                connectMatrixClient();
              }
            }, 3000);
            break;
        }
      });

      // Set up encryption-related event listeners
      if (window.indexedDB) {
        // Handle new device notifications
        matrixClient.current.on('deviceVerificationChanged' as any, (userId: string, deviceId: string) => {
          console.log(`Device verification changed for ${userId}:${deviceId}`);
        });

        // Handle key verification requests
        matrixClient.current.on('crypto.verification.request' as any, (request: any) => {
          console.log('Received verification request from:', request.userId);
          // Auto-accept verification requests for now
          // In a production app, you might want to show a UI for the user to accept/reject
          request.accept();
        });

        // Handle verification completion
        matrixClient.current.on('crypto.verification.complete' as any, (verifier: any) => {
          console.log('Verification completed with:', verifier.userId);
          // Mark the device as verified
          matrixClient.current?.setDeviceVerified(verifier.userId, verifier.deviceId, true);
        });

        // Handle room key requests
        matrixClient.current.on('crypto.roomKeyRequest' as any, (request: any) => {
          console.log('Room key request received');
          // Auto-share room keys for now
          // In a production app, you might want to be more selective
          if (request.requestBody.action === 'request') {
            request.share();
          }
        });

        // Handle encryption errors
        matrixClient.current.on('crypto.warning' as any, (type: string, error: Error) => {
          console.warn(`Encryption warning: ${type}`, error);
        });
      }

      // Listen for room events (messages)
      matrixClient.current.on('Room.timeline' as any, (event: MatrixEvent, room: Room) => {
        try {
          // Only handle message events
          if (event.getType() === 'm.room.message') {
            const messageData = {
              type: 'new_message',
              message: {
                id: event.getId() || '',
                conversation_id: room.roomId,
                sender_id: event.getSender()?.substring(1).split(':')[0] || '', // Extract user ID from Matrix ID
                message_type: mapMatrixMessageTypeToNxtAcre(event.getContent().msgtype),
                content: event.getContent().body || '',
                is_edited: event.isRedacted(),
                created_at: new Date(event.getTs()).toISOString(),
                updated_at: new Date(event.getTs()).toISOString(),
                sender_first_name: room.getMember(event.getSender() || '')?.name.split(' ')[0] || '',
                sender_last_name: room.getMember(event.getSender() || '')?.name.split(' ').slice(1).join(' ') || '',
                attachments: event.getContent().url ? [{
                  id: event.getId() || '',
                  message_id: event.getId() || '',
                  file_name: event.getContent().filename || 'file',
                  file_type: event.getContent().mimetype || 'application/octet-stream',
                  file_size: event.getContent().size || 0,
                  file_url: matrixClient.current?.mxcUrlToHttp(event.getContent().url) || '',
                  created_at: new Date(event.getTs()).toISOString(),
                  updated_at: new Date(event.getTs()).toISOString()
                }] : []
              }
            };

            handleMatrixEvent(messageData);
          }
          // Handle typing notifications
          else if (event.getType() === 'm.typing') {
            const typingData = {
              type: 'typing',
              conversation_id: room.roomId,
              user_id: event.getSender()?.substring(1).split(':')[0] || '', // Extract user ID from Matrix ID
              is_typing: event.getContent().user_ids?.includes(event.getSender() || '') || false
            };

            handleMatrixEvent(typingData);
          }
          // Handle read receipts
          else if (event.getType() === 'm.receipt') {
            const content = event.getContent();
            for (const eventId in content) {
              if (content[eventId]['m.read']) {
                const readData = {
                  type: 'read_receipt',
                  conversation_id: room.roomId,
                  message_ids: [eventId],
                  user_id: Object.keys(content[eventId]['m.read'])[0]?.substring(1).split(':')[0] || '' // Extract user ID from Matrix ID
                };

                handleMatrixEvent(readData);
              }
            }
          }
          // Handle presence (online status)
          else if (event.getType() === 'm.presence') {
            const presenceData = {
              type: 'online_status',
              user_id: event.getSender()?.substring(1).split(':')[0] || '', // Extract user ID from Matrix ID
              is_online: event.getContent().presence === 'online'
            };

            handleMatrixEvent(presenceData);
          }
        } catch (error) {
          console.error('Error handling Matrix event:', error);
        }
      });

      // Initialize crypto before starting the client
      if (window.indexedDB) {
        try {
          await matrixClient.current.initCrypto();
          console.log('Matrix encryption initialized successfully');
        } catch (cryptoError) {
          console.error('Error initializing Matrix encryption:', cryptoError);
          // Continue without encryption if it fails
        }
      }

      // Start client and sync
      await matrixClient.current.startClient();

      // Clean up on unmount
      return () => {
        if (matrixClient.current) {
          matrixClient.current.stopClient();
          matrixClient.current.removeAllListeners();
        }
      };
    } catch (error) {
      console.error('Error connecting to Matrix client:', error);
    }
  }, [user, getToken]);

  // Helper function to map Matrix message types to NxtAcre message types
  const mapMatrixMessageTypeToNxtAcre = (matrixType: string): 'text' | 'image' | 'file' | 'system' | 'task' => {
    switch (matrixType) {
      case 'm.text':
        return 'text';
      case 'm.image':
        return 'image';
      case 'm.file':
        return 'file';
      case 'm.notice':
        return 'system';
      default:
        return 'text';
    }
  };

  // Mark messages as read
  const markAsRead = useCallback(async (conversationId: string, messageIds: string[]) => {
    if (!user || !matrixClient.current) return;

    try {
      // Send read receipts via Matrix client
      // Use a workaround for the Matrix client's type system
      const sendReadReceiptFn = matrixClient.current.sendReadReceipt as any;
      for (const messageId of messageIds) {
        await sendReadReceiptFn.call(matrixClient.current, conversationId, messageId);
      }

      // Update local state
      setMessages(prevMessages => 
        prevMessages.map(msg => 
          messageIds.includes(msg.id)
            ? { 
                ...msg, 
                read_status: (msg.read_status || []).map(status => 
                  status.user_id === user.id
                    ? { ...status, is_read: true, read_at: new Date().toISOString() }
                    : status
                ) 
              }
            : msg
        )
      );

      // Update unread count for the conversation
      setConversations(prevConversations => 
        prevConversations.map(conv => 
          conv.id === conversationId
            ? { ...conv, unread_count: 0 }
            : conv
        )
      );
    } catch (error) {
      console.error('Error marking messages as read:', error);
      setError('Failed to mark messages as read');
    }
  }, [user]);

  // Handle Matrix events
  const handleMatrixEvent = useCallback((data: WebSocketMessage) => {
    switch (data.type) {
      case 'new_message':
        // Add new message to state if it's for the active conversation
        if (activeConversation && data.message.conversation_id === activeConversation.id) {
          setMessages(prevMessages => [...prevMessages, data.message]);

          // Mark the message as read since the conversation is active
          if (user) {
            markAsRead(data.message.conversation_id, [data.message.id]);
          }
        } else {
          // Update unread count for the conversation
          setConversations(prevConversations => 
            prevConversations.map(conv => 
              conv.id === data.message.conversation_id
                ? { 
                    ...conv, 
                    unread_count: (conv.unread_count || 0) + 1 
                  }
                : conv
            )
          );

          // If the user is not viewing this conversation, we'll need to notify them
          // The actual email notification will be handled by the backend via the alert rule
          // we set up in setupEmailNotificationRule

          // Show an in-app notification for the new message
          const conversation = conversations.find(conv => conv.id === data.message.conversation_id);
          if (conversation) {
            // Get sender name
            const senderName = data.message.sender_first_name && data.message.sender_last_name
              ? `${data.message.sender_first_name} ${data.message.sender_last_name}`
              : data.message.sender_email || 'Someone';

            // Get conversation name
            const conversationName = conversation.name || 
              (conversation.participants && conversation.participants.length > 0
                ? conversation.participants
                    .filter(p => p.id !== user?.id)
                    .map(p => `${p.first_name} ${p.last_name}`)
                    .join(', ')
                : 'Chat');

            // Show notification
            showNotification({
              type: 'info',
              title: `New message from ${senderName}`,
              message: data.message.content.length > 50 
                ? `${data.message.content.substring(0, 50)}...` 
                : data.message.content,
              onClick: () => {
                // Set this conversation as active when notification is clicked
                setActiveConversation(conversation);
                // Mark the message as read
                if (user) {
                  markAsRead(data.message.conversation_id, [data.message.id]);
                }
              }
            });
          }

          // Email notification will be handled by the backend based on the alert rule
          if (user && !document.hasFocus()) {
            // This is now handled by the notification system
          }
        }
        break;

      case 'typing':
        // Handle typing indicator
        if (activeConversation && data.conversation_id === activeConversation.id) {
          // Update typing status for the user
          const typingUser = {
            id: data.user_id,
            is_typing: data.is_typing
          };

          setTypingUsers(prev => {
            // If user is typing, add/update them in the list
            if (data.is_typing) {
              const existingIndex = prev.findIndex(u => u.id === data.user_id);
              if (existingIndex >= 0) {
                const newTypingUsers = [...prev];
                newTypingUsers[existingIndex] = typingUser;
                return newTypingUsers;
              } else {
                return [...prev, typingUser];
              }
            } else {
              // If user stopped typing, remove them from the list
              return prev.filter(u => u.id !== data.user_id);
            }
          });

          // Clear typing status after a timeout
          if (data.is_typing) {
            const timeoutId = typingTimeoutsRef.current[data.user_id];
            if (timeoutId) {
              clearTimeout(timeoutId);
            }

            typingTimeoutsRef.current[data.user_id] = setTimeout(() => {
              setTypingUsers(prev => prev.filter(u => u.id !== data.user_id));
            }, 5000); // Clear after 5 seconds of no updates
          }
        }
        break;

      case 'read_receipt':
        // Update read status for messages
        if (activeConversation && data.conversation_id === activeConversation.id) {
          setMessages(prevMessages => 
            prevMessages.map(msg => 
              data.message_ids.includes(msg.id)
                ? { 
                    ...msg, 
                    read_status: (msg.read_status || []).map(status => 
                      status.user_id === data.user_id
                        ? { ...status, is_read: true, read_at: new Date().toISOString() }
                        : status
                    ) 
                  }
                : msg
            )
          );
        }
        break;

      case 'online_status':
        // Update online status for users
        setOnlineUsers(prev => {
          const newOnlineUsers = { ...prev };
          newOnlineUsers[data.user_id] = data.is_online;
          return newOnlineUsers;
        });
        break;

      default:
        console.log('Unknown Matrix event type:', data.type);
    }
  }, [activeConversation, user, markAsRead, conversations, showNotification]);

  // Connect to Matrix client on mount
  useEffect(() => {
    let cleanupFn: (() => void) | undefined;

    // Use an async function to handle the Promise
    const setupMatrixClient = async () => {
      cleanupFn = await connectMatrixClient();
    };

    // Call the async function
    setupMatrixClient();

    // Return cleanup function
    return () => {
      if (cleanupFn) cleanupFn();
    };
  }, [connectMatrixClient]);

  // Load conversations
  const loadConversations = useCallback(async () => {
    if (!user || !matrixClient.current) return;

    setIsLoading(true);
    setError(null);

    try {
      // Get rooms from Matrix client
      const rooms = matrixClient.current.getRooms();

      // Transform rooms to conversations
      const loadedConversations: Conversation[] = rooms.map(room => {
        // Get farm ID from room state
        const farmIdEvent = room.currentState.getStateEvents('m.room.farm_id', '');
        const farmId = farmIdEvent ? farmIdEvent.getContent().farm_id : '';

        // Get room type
        let type: 'direct' | 'group' | 'channel' = 'group';
        if (room.getJoinRule() === 'public') {
          type = 'channel';
        } else if (room.getMembers().length === 2) {
          type = 'direct';
        }

        // Get participants
        const participants: User[] = room.getJoinedMembers().map(member => {
          const userId = member.userId.substring(1).split(':')[0]; // Extract user ID from Matrix ID
          const nameParts = member.name.split(' ');
          const firstName = nameParts[0] || '';
          const lastName = nameParts.slice(1).join(' ') || '';
          return {
            id: userId,
            first_name: firstName,
            last_name: lastName,
            firstName: firstName,
            lastName: lastName,
            email: member.rawDisplayName || '',
            is_online: (member as any).presence === 'online'
          };
        });

        // Get unread count
        const unreadCount = room.getUnreadNotificationCount();

        return {
          id: room.roomId,
          name: room.name || '',
          type,
          farm_id: farmId,
          created_by: room.getCreator()?.substring(1).split(':')[0] || '', // Extract user ID from Matrix ID
          is_pinned: false, // This would need to be stored separately
          created_at: new Date((room as any).getCreationTs ? (room as any).getCreationTs() : Date.now()).toISOString(),
          updated_at: new Date((room as any).getLastActiveTs ? (room as any).getLastActiveTs() : Date.now()).toISOString(),
          participants,
          unread_count: unreadCount
        };
      });

      // Filter conversations by farm ID if needed
      const filteredConversations = user.farm_id
        ? loadedConversations.filter(conv => conv.farm_id === user.farm_id)
        : loadedConversations;

      setConversations(filteredConversations);
    } catch (error) {
      console.error('Error loading conversations:', error);
      setError('Failed to load conversations');
    } finally {
      setIsLoading(false);
    }
  }, [user]);

  // Load messages for a conversation
  const loadMessages = useCallback(async (conversationId: string) => {
    if (!user || !matrixClient.current) return;

    setIsLoading(true);
    setError(null);

    try {
      // Get room from Matrix client
      const room = matrixClient.current.getRoom(conversationId);

      if (!room) {
        throw new Error(`Room ${conversationId} not found`);
      }

      // Get timeline events from room
      const timelineEvents = room.getLiveTimeline().getEvents();

      // Transform timeline events to messages
      const loadedMessages: Message[] = timelineEvents
        .filter(event => event.getType() === 'm.room.message')
        .map(event => {
          const sender = room.getMember(event.getSender() || '');
          const senderName = sender ? sender.name.split(' ') : ['', ''];

          // Check for attachments
          const content = event.getContent();
          const attachments: Attachment[] = [];

          if (content.url) {
            attachments.push({
              id: event.getId() || '',
              message_id: event.getId() || '',
              file_name: content.filename || 'file',
              file_type: content.mimetype || 'application/octet-stream',
              file_size: content.size || 0,
              file_url: matrixClient.current.mxcUrlToHttp(content.url) || '',
              thumbnail_url: content.thumbnail_url ? matrixClient.current.mxcUrlToHttp(content.thumbnail_url) : undefined,
              created_at: new Date(event.getTs()).toISOString(),
              updated_at: new Date(event.getTs()).toISOString()
            });
          }

          // Check for reactions
          const reactions: Reaction[] = [];
          const relationEvents = (room.getUnfilteredTimelineSet() as any).getRelationsForEvent?.(
            event.getId() || '',
            'm.annotation',
            'm.reaction'
          );

          if (relationEvents) {
            relationEvents.getRelations().forEach(relationEvent => {
              const relationContent = relationEvent.getContent();
              const relationSender = room.getMember(relationEvent.getSender() || '');
              const relationSenderName = relationSender ? relationSender.name.split(' ') : ['', ''];

              reactions.push({
                id: relationEvent.getId() || '',
                message_id: event.getId() || '',
                user_id: relationEvent.getSender()?.substring(1).split(':')[0] || '',
                reaction: relationContent.key || '',
                created_at: new Date(relationEvent.getTs()).toISOString(),
                updated_at: new Date(relationEvent.getTs()).toISOString(),
                first_name: relationSenderName[0] || '',
                last_name: relationSenderName.slice(1).join(' ') || '',
                email: relationSender?.rawDisplayName || ''
              });
            });
          }

          // Check for read receipts
          const readStatus: ReadStatus[] = [];
          const receipts = room.getReceiptsForEvent(event);

          if (receipts) {
            receipts.forEach(receipt => {
              if (receipt.type === 'm.read') {
                const receiptUser = room.getMember(receipt.userId);
                const receiptUserName = receiptUser ? receiptUser.name.split(' ') : ['', ''];

                readStatus.push({
                  id: `${event.getId()}_${receipt.userId}`,
                  message_id: event.getId() || '',
                  user_id: receipt.userId.substring(1).split(':')[0] || '',
                  is_read: true,
                  read_at: new Date(receipt.data.ts).toISOString(),
                  created_at: new Date(receipt.data.ts).toISOString(),
                  updated_at: new Date(receipt.data.ts).toISOString(),
                  first_name: receiptUserName[0] || '',
                  last_name: receiptUserName.slice(1).join(' ') || '',
                  email: receiptUser?.rawDisplayName || ''
                });
              }
            });
          }

          return {
            id: event.getId() || '',
            conversation_id: conversationId,
            sender_id: event.getSender()?.substring(1).split(':')[0] || '',
            message_type: mapMatrixMessageTypeToNxtAcre(content.msgtype),
            content: content.body || '',
            is_edited: event.isRedacted(),
            parent_message_id: content['m.relates_to']?.event_id,
            created_at: new Date(event.getTs()).toISOString(),
            updated_at: new Date(event.getTs()).toISOString(),
            sender_first_name: senderName[0] || '',
            sender_last_name: senderName.slice(1).join(' ') || '',
            sender_email: sender?.rawDisplayName || '',
            attachments: attachments.length > 0 ? attachments : undefined,
            reactions: reactions.length > 0 ? reactions : undefined,
            read_status: readStatus.length > 0 ? readStatus : undefined
          };
        });

      // Sort messages by timestamp
      loadedMessages.sort((a, b) => 
        new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
      );

      setMessages(loadedMessages);

      // Mark all messages as read since we're viewing the conversation
      const unreadMessageIds = loadedMessages
        .filter(msg => !msg.read_status?.some(status => status.user_id === user.id && status.is_read))
        .map(msg => msg.id);

      if (unreadMessageIds.length > 0) {
        markAsRead(conversationId, unreadMessageIds);
      }
    } catch (error) {
      console.error('Error loading messages:', error);
      setError('Failed to load messages');
    } finally {
      setIsLoading(false);
    }
  }, [user, markAsRead]);

  // Send a message
  const sendMessage = useCallback(async (conversationId: string, content: string, messageType: string = 'text') => {
    if (!user || !matrixClient.current) return;

    try {
      // Map NxtAcre message type to Matrix message type
      const matrixMessageType = mapNxtAcreMessageTypeToMatrix(messageType);

      // Create message content
      const messageContent = {
        msgtype: matrixMessageType,
        body: content
      };

      // Send message via Matrix client
      const response = await matrixClient.current.sendEvent(
        conversationId,
        'm.room.message' as any,
        messageContent
      );

      // Return message data in the format expected by the application
      return {
        id: response.event_id,
        conversation_id: conversationId,
        sender_id: user.id,
        message_type: messageType,
        content: content,
        is_edited: false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        sender_first_name: user.firstName,
        sender_last_name: user.lastName,
        sender_email: user.email
      };
    } catch (error) {
      console.error('Error sending message:', error);
      setError('Failed to send message');
      throw error;
    }
  }, [user]);

  // Helper function to map NxtAcre message types to Matrix message types
  const mapNxtAcreMessageTypeToMatrix = (nxtAcreType: string): string => {
    switch (nxtAcreType) {
      case 'text':
        return 'm.text';
      case 'image':
        return 'm.image';
      case 'file':
        return 'm.file';
      case 'system':
        return 'm.notice';
      case 'task':
        return 'm.text'; // Custom type, map to text for now
      default:
        return 'm.text';
    }
  };

  // Create a new conversation
  const createConversation = useCallback(async (
    participants: string[], 
    name?: string, 
    type: 'direct' | 'group' | 'channel' = 'direct'
  ) => {
    if (!user || !matrixClient.current) return null;

    try {
      // Map NxtAcre conversation type to Matrix room visibility and preset
      let visibility: any = 'private';
      let preset: any = 'private_chat';

      if (type === 'channel') {
        visibility = 'public';
        preset = 'public_chat';
      } else if (type === 'direct') {
        preset = 'trusted_private_chat';
      }

      // Create room via Matrix client with encryption enabled
      const { room_id } = await matrixClient.current.createRoom({
        visibility,
        preset,
        name: name || undefined,
        initial_state: [
          {
            type: 'm.room.farm_id',
            state_key: '',
            content: { farm_id: user.farm_id || '' }
          },
          // Enable encryption for the room
          {
            type: 'm.room.encryption',
            state_key: '',
            content: {
              algorithm: 'm.megolm.v1.aes-sha2',
              rotation_period_ms: 604800000, // 1 week
              rotation_period_msgs: 100
            }
          }
        ]
      });

      // Invite participants
      for (const participantId of participants) {
        if (participantId !== user.id) {
          await matrixClient.current.invite(
            room_id,
            `@${participantId}:${MATRIX_DOMAIN}`
          );
        }
      }

      // Create conversation object in the format expected by the application
      const newConversation: Conversation = {
        id: room_id,
        name: name || '',
        type,
        farm_id: user.farm_id || '',
        created_by: user.id,
        is_pinned: false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        participants: participants.map(id => ({
          id,
          first_name: '', // These would be populated when the room is synced
          last_name: '',
          firstName: '',
          lastName: '',
          email: ''
        })),
        unread_count: 0
      };

      // Add the new conversation to state
      setConversations(prev => [newConversation, ...prev]);

      return newConversation;
    } catch (error) {
      console.error('Error creating conversation:', error);
      setError('Failed to create conversation');
      return null;
    }
  }, [user]);

  // Add a reaction to a message
  const addReaction = useCallback(async (conversationId: string, messageId: string, reaction: string) => {
    if (!user || !matrixClient.current) return;

    try {
      // Send reaction via Matrix client
      await matrixClient.current.sendEvent(
        conversationId,
        'm.reaction' as any,
        {
          'm.relates_to': {
            rel_type: 'm.annotation',
            event_id: messageId,
            key: reaction
          }
        }
      );

      // Update local state - this will be handled by the timeline event listener
      // but we can also update it here for immediate feedback
      setMessages(prevMessages => {
        const targetMessage = prevMessages.find(msg => msg.id === messageId);
        if (!targetMessage) return prevMessages;

        const existingReactions = targetMessage.reactions || [];
        const existingReactionIndex = existingReactions.findIndex(
          r => r.user_id === user.id && r.reaction === reaction
        );

        // If user already reacted with this emoji, don't add it again
        if (existingReactionIndex >= 0) return prevMessages;

        // Add the new reaction
        const newReaction: Reaction = {
          id: `temp_${Date.now()}`, // Temporary ID until we get the real one from Matrix
          message_id: messageId,
          user_id: user.id,
          reaction,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          first_name: user.firstName,
          last_name: user.lastName,
          email: user.email
        };

        return prevMessages.map(msg => 
          msg.id === messageId
            ? { 
                ...msg, 
                reactions: [...(msg.reactions || []), newReaction] 
              }
            : msg
        );
      });
    } catch (error) {
      console.error('Error adding reaction:', error);
      setError('Failed to add reaction');
    }
  }, [user]);

  // Upload a file attachment
  const uploadAttachment = useCallback(async (conversationId: string, file: File) => {
    if (!user || !matrixClient.current) throw new Error('User not authenticated or Matrix client not initialized');

    try {
      // Read file as ArrayBuffer
      const arrayBuffer = await new Promise<ArrayBuffer>((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result as ArrayBuffer);
        reader.onerror = reject;
        reader.readAsArrayBuffer(file);
      });

      // Upload file to Matrix media repository
      const uploadResponse = await matrixClient.current.uploadContent(
        new Uint8Array(arrayBuffer),
        {
          name: file.name,
          type: file.type,
          // Cast to any to handle onlyContentUri property
        } as any
      );

      // Get the MXC URI from the response
      const mxcUri = uploadResponse.content_uri;

      // Determine message type based on file type
      let msgtype = 'm.file';
      if (file.type.startsWith('image/')) {
        msgtype = 'm.image';
      } else if (file.type.startsWith('audio/')) {
        msgtype = 'm.audio';
      } else if (file.type.startsWith('video/')) {
        msgtype = 'm.video';
      }

      // Create message content
      const content = {
        msgtype,
        body: file.name,
        url: mxcUri,
        filename: file.name,
        mimetype: file.type,
        size: file.size
      };

      // Send message with attachment
      const eventResponse = await matrixClient.current.sendEvent(
        conversationId,
        'm.room.message' as any,
        content
      );

      // Convert MXC URI to HTTP URL for client use
      const httpUrl = matrixClient.current.mxcUrlToHttp(mxcUri);

      // Return attachment data in the format expected by the application
      return {
        id: eventResponse.event_id,
        message_id: eventResponse.event_id,
        file_name: file.name,
        file_type: file.type,
        file_size: file.size,
        file_url: httpUrl || '',
        thumbnail_url: file.type.startsWith('image/') ? httpUrl : undefined,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
    } catch (error) {
      console.error('Error uploading attachment:', error);
      setError('Failed to upload attachment');
      throw error;
    }
  }, [user]);

  // Send typing indicator
  const sendTypingIndicator = useCallback((conversationId: string, isTyping: boolean) => {
    if (!user || !matrixClient.current) return;

    try {
      // Send typing notification via Matrix client
      matrixClient.current.sendTyping(conversationId, isTyping, isTyping ? 5000 : undefined);

      // If user is typing, set a timeout to automatically clear typing status
      if (isTyping) {
        const timeoutId = setTimeout(() => {
          if (matrixClient.current) {
            matrixClient.current.sendTyping(conversationId, false, undefined);
          }
        }, 5000); // Clear after 5 seconds of no updates

        // Store the timeout ID so we can clear it if needed
        const userId = user.id;
        const existingTimeoutId = typingTimeoutsRef.current[userId];
        if (existingTimeoutId) {
          clearTimeout(existingTimeoutId);
        }
        typingTimeoutsRef.current[userId] = timeoutId;
      } else {
        // If user stopped typing, clear any existing timeout
        const userId = user.id;
        const existingTimeoutId = typingTimeoutsRef.current[userId];
        if (existingTimeoutId) {
          clearTimeout(existingTimeoutId);
          delete typingTimeoutsRef.current[userId];
        }
      }
    } catch (error) {
      console.error('Error sending typing indicator:', error);
    }
  }, [user]);

  // Set up email notification rule for unread messages
  const setupEmailNotificationRule = useCallback(async () => {
    if (!user || notificationRuleSetup) return;

    // Only proceed if farm_id is available
    if (!user.farm_id) {
      console.log('Skipping alert rule creation: FarmId is required');
      return;
    }

    try {
      // Create an alert rule for unread chat messages
      await createUnreadMessagesAlertRule(
        user.farm_id,
        user.id,
        user.email
      );

      setNotificationRuleSetup(true);
    } catch (error) {
      console.error('Error setting up email notification rule:', error);
      // Don't set error state here to avoid disrupting the chat experience
    }
  }, [user, notificationRuleSetup]);

  // Load conversations on mount
  useEffect(() => {
    if (user) {
      loadConversations();
      setupEmailNotificationRule();
    }
  }, [user, loadConversations, setupEmailNotificationRule]);

  // Context value
  const value = {
    conversations,
    activeConversation,
    messages,
    isLoading,
    error,
    loadConversations,
    loadMessages,
    sendMessage,
    createConversation,
    markAsRead,
    addReaction,
    uploadAttachment,
    setActiveConversation,
    isConnected,
    typingUsers,
    onlineUsers,
    sendTypingIndicator
  };

  return (
    <ChatContext.Provider value={value}>
      {children}
    </ChatContext.Provider>
  );
};

// Custom hook to use the chat context
export const useChat = () => useContext(ChatContext);

import { createContext, useContext } from 'react';
import type { User } from '../types/user';

// Define the shape of our context
export interface AuthContextType {
  user: User | null;
  token: string | null;
  loading: boolean;
  error: string | null;
  isImpersonating: boolean;
  adminId: string | null;
  login: (email: string, password: string, farmSubdomain?: string | null) => Promise<{ requireTwoFactor?: boolean, userId?: string, farmSubdomain?: string, farmId?: string }>;
  register: (email: string, password: string, firstName: string, lastName: string, phoneNumber?: string, userType?: string, isBusinessOwner?: boolean, farmName?: string, subdomain?: string) => Promise<{ user?: { id: string } } | any>;
  logout: () => void;
  verifyTwoFactor: (userId: string, token: string, farmSubdomain?: string | null, farmId?: string | null, method?: string | null, rememberDevice?: boolean) => Promise<{ farmSubdomain: any, farmId: any }>;
  setupTwoFactor: () => Promise<{ secret: string, qrCode: string }>;
  setupSMS2FA: () => Promise<void>;
  confirmTwoFactor: (token: string) => Promise<void>;
  confirmTwoFactorWithConsecutiveCodes: (code1: string, code2: string) => Promise<void>;
  disableTwoFactor: (token: string) => Promise<void>;
  forgotPassword: (email: string) => Promise<void>;
  resetPassword: (token: string, password: string) => Promise<void>;
  updateUser: (userData: Partial<User>) => void;
  clearError: () => void;
  extendSession: () => void;
  impersonateUser: (userId: string) => Promise<void>;
  endImpersonation: () => Promise<void>;
  sendPhoneVerificationCode: (phoneNumber: string) => Promise<void>;
  verifyPhoneNumber: (code: string) => Promise<void>;
  getToken: () => Promise<string | null>;
}

// Create the context with a default value
export const AuthContext = createContext<AuthContextType>({
  user: null,
  token: null,
  loading: false,
  error: null,
  isImpersonating: false,
  adminId: null,
  login: async () => ({}),
  register: async () => {},
  logout: () => {},
  verifyTwoFactor: async () => ({ farmSubdomain: null, farmId: null }),
  setupTwoFactor: async () => ({ secret: '', qrCode: '' }),
  setupSMS2FA: async () => {},
  confirmTwoFactor: async () => {},
  confirmTwoFactorWithConsecutiveCodes: async () => {},
  disableTwoFactor: async () => {},
  forgotPassword: async () => {},
  resetPassword: async () => {},
  updateUser: () => {},
  clearError: () => {},
  extendSession: () => {},
  impersonateUser: async () => {},
  endImpersonation: async () => {},
  sendPhoneVerificationCode: async () => {},
  verifyPhoneNumber: async () => {},
  getToken: async () => null,
});

// Custom hook to use the auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// Export the provider from a separate file
export { AuthProvider } from './AuthProvider';

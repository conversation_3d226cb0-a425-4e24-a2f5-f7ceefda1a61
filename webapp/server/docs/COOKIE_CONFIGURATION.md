# Cross-Subdomain Cookie Configuration

This document explains the cookie configuration implemented for cross-subdomain authentication in the NxtAcre application.

## Overview

The application uses a multi-subdomain architecture where:
- Each farm has its own subdomain (e.g., `farm1.nxtacre.com`)
- Global admin uses the admin subdomain (`admin.nxtacre.com`)
- Businesses/vets/suppliers without active farms use the app subdomain (`app.nxtacre.com`)
- Users can switch between subdomains while maintaining their authentication state

## Cookie Configuration

### Server-Side Configuration

#### Environment Variables
- `MAIN_DOMAIN`: The main domain for cookie sharing (e.g., `nxtacre.com`)
- `VITE_MAIN_DOMAIN`: Fallback for the main domain (frontend variable)

#### Cookie Settings
All authentication cookies are set with the following properties:

```javascript
{
  domain: '.nxtacre.com',        // Allows sharing across all subdomains
  secure: true,                  // Requires HTTPS (except in development)
  sameSite: 'none',             // Allows cross-site requests
  httpOnly: true,               // Prevents JavaScript access (for auth tokens)
  path: '/',                    // Available on all paths
  maxAge: 900000                // 15 minutes for auth tokens, 7 days for refresh tokens
}
```

#### Key Functions

1. **getCookieDomain()**: Returns the properly formatted domain (`.nxtacre.com`)
2. **setSecureCookie()**: Helper function that sets cookies with consistent security options
3. **Logout cookie clearing**: Clears cookies with both domain and fallback options

### Frontend Configuration

#### Storage Utilities
The frontend uses `storageUtils.ts` to handle cross-subdomain data sharing:

- Stores data in both localStorage and cookies
- Uses `sameSite: 'none'` for cross-subdomain compatibility
- Automatically detects HTTPS and adjusts security settings
- Provides fallback clearing for both domain and non-domain cookies

#### Cookie Settings
Frontend cookies use:

```javascript
{
  domain: '.nxtacre.com',
  secure: true,                 // Only on HTTPS
  sameSite: 'none',            // Cross-subdomain sharing
  path: '/',
  expires: '7 days'
}
```

## Security Considerations

### HTTPS Requirement
- `sameSite: 'none'` requires `secure: true`
- All production environments must use HTTPS
- Local development uses `sameSite: 'lax'` as fallback

### HttpOnly Flags
- Authentication tokens use `httpOnly: true` to prevent XSS attacks
- Non-sensitive cookies (like UI preferences) can use `httpOnly: false`

### Domain Validation
- Cookies are only set for the configured main domain
- Prevents cookie injection from external domains

## Implementation Details

### Authentication Flow
1. User logs in on any subdomain
2. Server sets auth cookies with domain `.nxtacre.com`
3. Cookies are automatically available on all subdomains
4. Frontend relies on HTTP-only cookies for authentication
5. User can switch subdomains without re-authentication

### Hybrid Authentication Support
The system supports both HTTP-only cookies and Authorization headers:
- **Primary**: HTTP-only cookies for secure cross-subdomain authentication
- **Fallback**: Authorization header for backward compatibility
- Middleware checks cookies first, then falls back to headers

### Frontend Changes
- Tokens are no longer stored in localStorage for security
- User data (non-sensitive) is still stored in localStorage for quick access
- Authentication state is validated via `/auth/check-auth` endpoint
- Frontend automatically includes cookies with `credentials: 'include'`

### Logout Process
1. Server clears HTTP-only cookies with proper domain settings
2. Fallback clearing without domain for edge cases
3. Frontend clears non-sensitive localStorage data only

### Token Refresh
- Refresh tokens are stored as httpOnly cookies
- Automatically available across subdomains
- Secure refresh without exposing tokens to JavaScript

## Testing

### Manual Testing
1. Log in on one subdomain (e.g., `app.nxtacre.com`)
2. Navigate to another subdomain (e.g., `farm1.nxtacre.com`)
3. Verify authentication persists
4. Test logout from any subdomain

### Automated Testing
Use the test script: `webapp/server/scripts/test-cookie-config.js`

```bash
cd webapp/server
node scripts/test-cookie-config.js
```

Test endpoints:
- `POST /test/set-cookies` - Set test cookies
- `GET /test/read-cookies` - Read cookies from current subdomain
- `POST /test/clear-cookies` - Clear test cookies
- `GET /test/health` - Check configuration

### Frontend Testing
Use the HTML test page: `webapp/test-cookie-auth.html`

1. Open the file in a browser
2. Test login functionality
3. Verify authentication status
4. Test logout functionality
5. Check cookie information

The test page verifies:
- HTTP-only cookies are set during login
- Cookies are sent with subsequent requests
- Authentication works without localStorage tokens
- Logout properly clears cookies

## Troubleshooting

### Common Issues

1. **Cookies not sharing across subdomains**
   - Check `MAIN_DOMAIN` environment variable
   - Verify HTTPS is enabled in production
   - Ensure `sameSite: 'none'` and `secure: true`

2. **Authentication lost on subdomain switch**
   - Check cookie domain setting (should start with `.`)
   - Verify CORS configuration allows credentials
   - Check browser developer tools for cookie presence

3. **Local development issues**
   - Use `localhost` or proper domain mapping
   - Consider using `sameSite: 'lax'` for local testing
   - Check if HTTPS is required for your test setup

### Browser Developer Tools
Check cookies in browser dev tools:
1. Open Application/Storage tab
2. Check Cookies section
3. Verify domain, secure, and sameSite settings
4. Test on different subdomains

## Migration Notes

### From Previous Configuration
- Updated hardcoded domain to use environment variables
- Changed `sameSite` from `'strict'` to `'none'` for cross-subdomain support
- Added fallback cookie clearing for better compatibility
- Unified cookie settings between server and frontend

### Environment Variables
Add to your `.env` file:
```
MAIN_DOMAIN=nxtacre.com
```

## Best Practices

1. **Always use HTTPS in production**
2. **Set appropriate cookie expiration times**
3. **Use httpOnly for sensitive cookies**
4. **Implement proper CORS configuration**
5. **Test across all subdomains**
6. **Monitor cookie size limits**
7. **Implement secure logout on all subdomains**

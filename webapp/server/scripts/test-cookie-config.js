#!/usr/bin/env node

/**
 * Test script to verify cookie configuration for cross-subdomain compatibility
 * This script tests the cookie settings to ensure they work across subdomains
 */

import express from 'express';
import cookieParser from 'cookie-parser';
import cors from 'cors';
import dotenv from 'dotenv';

dotenv.config();

const app = express();
const PORT = 3003; // Use a different port for testing

// Middleware
app.use(cookieParser());
app.use(express.json());

// CORS configuration for testing
app.use(cors({
  origin: function(origin, callback) {
    // Allow all origins for testing
    callback(null, true);
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'Accept'],
  exposedHeaders: ['Set-Cookie'],
}));

// Helper function to get the cookie domain
const getCookieDomain = () => {
  const mainDomain = process.env.MAIN_DOMAIN || process.env.VITE_MAIN_DOMAIN || 'nxtacre.com';
  return `.${mainDomain}`;
};

// Test endpoint to set cookies
app.post('/test/set-cookies', (req, res) => {
  const { testValue = 'test-value-123' } = req.body;
  
  console.log('Setting test cookies...');
  console.log('Main domain:', process.env.MAIN_DOMAIN || process.env.VITE_MAIN_DOMAIN || 'nxtacre.com');
  console.log('Cookie domain:', getCookieDomain());
  
  // Set test cookies with cross-subdomain configuration
  res.cookie('test_auth_token', testValue, {
    httpOnly: true,
    secure: true,
    sameSite: 'none',
    domain: getCookieDomain(),
    maxAge: 15 * 60 * 1000, // 15 minutes
    path: '/'
  });
  
  res.cookie('test_refresh_token', `refresh_${testValue}`, {
    httpOnly: true,
    secure: true,
    sameSite: 'none',
    domain: getCookieDomain(),
    maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
    path: '/'
  });
  
  // Also set a non-httpOnly cookie for frontend testing
  res.cookie('test_frontend_token', `frontend_${testValue}`, {
    httpOnly: false,
    secure: true,
    sameSite: 'none',
    domain: getCookieDomain(),
    maxAge: 15 * 60 * 1000,
    path: '/'
  });
  
  res.json({
    success: true,
    message: 'Test cookies set successfully',
    cookieDomain: getCookieDomain(),
    cookies: {
      test_auth_token: testValue,
      test_refresh_token: `refresh_${testValue}`,
      test_frontend_token: `frontend_${testValue}`
    }
  });
});

// Test endpoint to read cookies
app.get('/test/read-cookies', (req, res) => {
  console.log('Reading cookies...');
  console.log('All cookies:', req.cookies);
  
  const cookies = {
    test_auth_token: req.cookies.test_auth_token,
    test_refresh_token: req.cookies.test_refresh_token,
    test_frontend_token: req.cookies.test_frontend_token
  };
  
  res.json({
    success: true,
    message: 'Cookies read successfully',
    cookies,
    allCookies: req.cookies,
    host: req.get('host'),
    origin: req.get('origin')
  });
});

// Test endpoint to clear cookies
app.post('/test/clear-cookies', (req, res) => {
  console.log('Clearing test cookies...');
  
  const cookieOptions = {
    domain: getCookieDomain(),
    secure: true,
    sameSite: 'none',
    httpOnly: true,
    path: '/'
  };
  
  // Clear cookies with domain
  res.clearCookie('test_auth_token', cookieOptions);
  res.clearCookie('test_refresh_token', cookieOptions);
  res.clearCookie('test_frontend_token', { ...cookieOptions, httpOnly: false });
  
  // Also clear without domain (fallback)
  res.clearCookie('test_auth_token', { secure: true, sameSite: 'none', httpOnly: true, path: '/' });
  res.clearCookie('test_refresh_token', { secure: true, sameSite: 'none', httpOnly: true, path: '/' });
  res.clearCookie('test_frontend_token', { secure: true, sameSite: 'none', httpOnly: false, path: '/' });
  
  res.json({
    success: true,
    message: 'Test cookies cleared successfully'
  });
});

// Health check endpoint
app.get('/test/health', (req, res) => {
  res.json({
    success: true,
    message: 'Cookie test server is running',
    environment: {
      NODE_ENV: process.env.NODE_ENV,
      MAIN_DOMAIN: process.env.MAIN_DOMAIN,
      VITE_MAIN_DOMAIN: process.env.VITE_MAIN_DOMAIN
    },
    cookieDomain: getCookieDomain()
  });
});

// Start the test server
app.listen(PORT, () => {
  console.log(`\n🍪 Cookie Test Server running on port ${PORT}`);
  console.log(`📍 Main domain: ${process.env.MAIN_DOMAIN || process.env.VITE_MAIN_DOMAIN || 'nxtacre.com'}`);
  console.log(`🌐 Cookie domain: ${getCookieDomain()}`);
  console.log(`\n📋 Test endpoints:`);
  console.log(`   POST http://localhost:${PORT}/test/set-cookies`);
  console.log(`   GET  http://localhost:${PORT}/test/read-cookies`);
  console.log(`   POST http://localhost:${PORT}/test/clear-cookies`);
  console.log(`   GET  http://localhost:${PORT}/test/health`);
  console.log(`\n🧪 To test cross-subdomain functionality:`);
  console.log(`   1. Set cookies on one subdomain`);
  console.log(`   2. Try to read them from another subdomain`);
  console.log(`   3. Verify they persist across subdomain switches\n`);
});

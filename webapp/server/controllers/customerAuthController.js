import jwt from 'jsonwebtoken';
import crypto from 'crypto';
import bcrypt from 'bcryptjs';
import nodemailer from 'nodemailer';
import fs from 'fs';
import path from 'path';
import Customer from '../models/Customer.js';
import Farm from '../models/Farm.js';
import { sequelize } from '../config/database.js';
import dotenv from 'dotenv';

dotenv.config();

// JWT configuration
const JWT_SECRET = process.env.JWT_SECRET || 'your_jwt_secret_key';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '15m';
const JWT_REFRESH_SECRET = process.env.JWT_REFRESH_SECRET || 'your_refresh_token_secret';
const JWT_REFRESH_EXPIRES_IN = process.env.JWT_REFRESH_EXPIRES_IN || '7d';
const ONE_TIME_TOKEN_EXPIRES_IN = '24h'; // One-time login links expire in 24 hours

// Email configuration
const transporter = nodemailer.createTransport({
  host: process.env.EMAIL_HOST,
  port: process.env.EMAIL_PORT,
  secure: process.env.EMAIL_PORT === '465',
  auth: {
    user: process.env.EMAIL_USER,
    pass: process.env.EMAIL_PASSWORD,
  },
});

// Helper function to generate tokens
const generateTokens = async (customerId, farmId) => {
  // Generate access token with shorter expiration
  const token = jwt.sign({ id: customerId, type: 'customer', farmId }, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN });

  // Generate refresh token
  const refreshToken = jwt.sign({ id: customerId, type: 'customer', farmId }, JWT_REFRESH_SECRET, { expiresIn: JWT_REFRESH_EXPIRES_IN });

  return { token, refreshToken };
};

// Customer login
export const login = async (req, res) => {
  try {
    const { email, password, farmId } = req.body;

    // Find customer by email and farm ID
    const customer = await Customer.findOne({
      where: { 
        email,
        farm_id: farmId,
        portal_access: true // Only allow customers with portal access to login
      }
    });

    if (!customer) {
      return res.status(401).json({ error: 'Invalid credentials or no portal access' });
    }

    // Validate password
    const isPasswordValid = await customer.validatePassword(password);
    if (!isPasswordValid) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }

    // Check if email is verified
    if (!customer.email_verified) {
      return res.status(403).json({ error: 'Email not verified. Please verify your email before logging in.' });
    }

    // Get farm information
    const farm = await Farm.findByPk(customer.farm_id);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Generate tokens
    const { token, refreshToken } = await generateTokens(customer.id, customer.farm_id);

    // Set refresh token as HTTP-only cookie
    res.cookie('refresh_token', refreshToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
    });

    // Return customer information and token
    return res.status(200).json({
      token,
      customer: {
        id: customer.id,
        name: customer.name,
        email: customer.email,
        farm: {
          id: farm.id,
          name: farm.name
        }
      }
    });
  } catch (error) {
    console.error('Customer login error:', error);
    return res.status(500).json({ error: 'An error occurred during login. Please try again later.' });
  }
};

// Register a new customer account
export const register = async (req, res) => {
  let transaction;

  try {
    transaction = await sequelize.transaction();

    const { email, password, name, farmId } = req.body;

    // Check if customer already exists
    const existingCustomer = await Customer.findOne({
      where: { email, farm_id: farmId },
      transaction
    });

    if (existingCustomer) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Customer with this email already exists' });
    }

    // Check if farm exists
    const farm = await Farm.findByPk(farmId, { transaction });
    if (!farm) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Generate email verification token
    const emailVerificationToken = crypto.randomBytes(32).toString('hex');
    const emailVerificationExpires = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

    // Create new customer
    const customer = await Customer.create({
      email,
      password_hash: password, // Will be hashed by the model hooks
      name,
      farm_id: farmId,
      email_verification_token: emailVerificationToken,
      email_verification_expires: emailVerificationExpires,
      portal_access: true // Enable portal access by default for self-registered customers
    }, { transaction });

    // Send verification email
    const verificationUrl = `${process.env.FRONTEND_URL}/customer/verify-email/${emailVerificationToken}`;

    await transporter.sendMail({
      from: process.env.EMAIL_FROM || '<EMAIL>',
      to: email,
      subject: 'Verify Your Email Address',
      html: `
        <h1>Email Verification</h1>
        <p>Hello ${name},</p>
        <p>Thank you for registering with ${farm.name}. Please verify your email address by clicking the link below:</p>
        <a href="${verificationUrl}">Verify Email</a>
        <p>This link will expire in 24 hours.</p>
        <p>If you did not request this, please ignore this email.</p>
      `
    });

    await transaction.commit();

    return res.status(201).json({
      message: 'Customer registered successfully. Please check your email to verify your account.',
      customerId: customer.id
    });
  } catch (error) {
    if (transaction) await transaction.rollback();
    console.error('Customer registration error:', error);
    return res.status(500).json({ error: 'Registration failed. Please try again later.' });
  }
};

// Verify email
export const verifyEmail = async (req, res) => {
  try {
    const { token } = req.params;

    // Find customer with this token
    const customer = await Customer.findOne({
      where: {
        email_verification_token: token,
        email_verification_expires: { [sequelize.Op.gt]: new Date() }
      }
    });

    if (!customer) {
      return res.status(400).json({ error: 'Invalid or expired verification token' });
    }

    // Update customer
    customer.email_verified = true;
    customer.email_verification_token = null;
    customer.email_verification_expires = null;
    await customer.save();

    return res.status(200).json({ message: 'Email verified successfully. You can now log in.' });
  } catch (error) {
    console.error('Email verification error:', error);
    return res.status(500).json({ error: 'Email verification failed. Please try again later.' });
  }
};

// Forgot password
export const forgotPassword = async (req, res) => {
  try {
    const { email, farmId } = req.body;

    // Find customer by email and farm ID
    const customer = await Customer.findOne({
      where: { email, farm_id: farmId }
    });

    if (!customer) {
      // Don't reveal that the customer doesn't exist
      return res.status(200).json({ message: 'If your email is registered, you will receive a password reset link.' });
    }

    // Generate reset token
    const resetToken = crypto.randomBytes(32).toString('hex');
    const resetExpires = new Date(Date.now() + 1 * 60 * 60 * 1000); // 1 hour

    // Update customer
    customer.reset_password_token = resetToken;
    customer.reset_password_expires = resetExpires;
    await customer.save();

    // Get farm information
    const farm = await Farm.findByPk(customer.farm_id);

    // Send reset email
    const resetUrl = `${process.env.FRONTEND_URL}/customer/reset-password/${resetToken}`;

    await transporter.sendMail({
      from: process.env.EMAIL_FROM || '<EMAIL>',
      to: email,
      subject: 'Reset Your Password',
      html: `
        <h1>Password Reset</h1>
        <p>Hello ${customer.name},</p>
        <p>You requested a password reset for your account with ${farm.name}. Please click the link below to reset your password:</p>
        <a href="${resetUrl}">Reset Password</a>
        <p>This link will expire in 1 hour.</p>
        <p>If you did not request this, please ignore this email and your password will remain unchanged.</p>
      `
    });

    return res.status(200).json({ message: 'If your email is registered, you will receive a password reset link.' });
  } catch (error) {
    console.error('Forgot password error:', error);
    return res.status(500).json({ error: 'Password reset request failed. Please try again later.' });
  }
};

// Reset password
export const resetPassword = async (req, res) => {
  try {
    const { token } = req.params;
    const { password } = req.body;

    // Find customer with this token
    const customer = await Customer.findOne({
      where: {
        reset_password_token: token,
        reset_password_expires: { [sequelize.Op.gt]: new Date() }
      }
    });

    if (!customer) {
      return res.status(400).json({ error: 'Invalid or expired reset token' });
    }

    // Update customer
    customer.password_hash = password; // Will be hashed by the model hooks
    customer.reset_password_token = null;
    customer.reset_password_expires = null;
    await customer.save();

    return res.status(200).json({ message: 'Password reset successfully. You can now log in with your new password.' });
  } catch (error) {
    console.error('Reset password error:', error);
    return res.status(500).json({ error: 'Password reset failed. Please try again later.' });
  }
};

// Refresh token
export const refreshToken = async (req, res) => {
  try {
    const { refresh_token } = req.cookies;

    if (!refresh_token) {
      return res.status(401).json({ error: 'No refresh token provided' });
    }

    // Verify refresh token
    const decoded = jwt.verify(refresh_token, JWT_REFRESH_SECRET);

    // Check if it's a customer token
    if (decoded.type !== 'customer') {
      return res.status(403).json({ error: 'Invalid token type' });
    }

    // Find customer
    const customer = await Customer.findByPk(decoded.id);

    if (!customer) {
      return res.status(401).json({ error: 'Invalid refresh token' });
    }

    // Generate new tokens
    const { token, refreshToken } = await generateTokens(customer.id, customer.farm_id);

    // Set new refresh token as HTTP-only cookie
    res.cookie('refresh_token', refreshToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
    });

    // Return new access token
    return res.status(200).json({ token });
  } catch (error) {
    console.error('Refresh token error:', error);
    return res.status(401).json({ error: 'Invalid refresh token' });
  }
};

// Helper function to get the cookie domain with proper formatting
const getCookieDomain = () => {
  const mainDomain = process.env.MAIN_DOMAIN || process.env.VITE_MAIN_DOMAIN || 'nxtacre.com';
  return `.${mainDomain}`;
};

// Logout
export const logout = async (req, res) => {
  try {
    // Clear refresh token cookie with proper domain settings
    const cookieOptions = {
      domain: getCookieDomain(),
      secure: true,
      sameSite: 'none',
      httpOnly: true,
      path: '/'
    };

    res.clearCookie('refresh_token', cookieOptions);

    // Also clear cookie without domain (fallback)
    res.clearCookie('refresh_token', { secure: true, sameSite: 'none', httpOnly: true, path: '/' });

    return res.status(200).json({ message: 'Logged out successfully' });
  } catch (error) {
    console.error('Logout error:', error);
    return res.status(500).json({ error: 'Logout failed' });
  }
};

// Send one-time login link
export const sendLoginLink = async (req, res) => {
  try {
    const { email, farmId, phoneNumber } = req.body;

    // Find customer by email or phone number and farm ID
    const whereClause = {
      farm_id: farmId,
      portal_access: true // Only allow customers with portal access
    };

    // Add either email or phone number to the where clause
    if (email) {
      whereClause.email = email;
    } else if (phoneNumber) {
      whereClause.phone = phoneNumber;
    } else {
      return res.status(400).json({ error: 'Email or phone number is required' });
    }

    const customer = await Customer.findOne({
      where: whereClause
    });

    if (!customer) {
      // Don't reveal that the customer doesn't exist
      return res.status(200).json({ 
        message: 'If your email or phone number is registered, you will receive a login link.' 
      });
    }

    // Get farm information
    const farm = await Farm.findByPk(customer.farm_id);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Generate one-time login token
    const loginToken = crypto.randomBytes(32).toString('hex');
    const loginTokenExpires = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

    // Update customer with login token
    customer.login_token = loginToken;
    customer.login_token_expires = loginTokenExpires;
    await customer.save();

    // Create login URL
    const loginUrl = `${process.env.FRONTEND_URL}/customer/login-with-token/${loginToken}`;

    // Get farm logo if available
    const farmLogo = farm.logo_url || null;

    // Read the email template
    const templatePath = path.join(process.cwd(), 'webapp', 'server', 'templates', 'emails', 'auth', 'customer-login-link.html');
    let emailTemplate = fs.readFileSync(templatePath, 'utf8');

    // Replace template variables
    emailTemplate = emailTemplate
      .replace(/{{customerName}}/g, customer.name)
      .replace(/{{farmName}}/g, farm.name)
      .replace(/{{loginUrl}}/g, loginUrl)
      .replace(/{{year}}/g, new Date().getFullYear())
      .replace(/{{customerEmail}}/g, customer.email);

    // Handle farm logo
    if (farmLogo) {
      emailTemplate = emailTemplate.replace(/{{#if farmLogo}}([\s\S]*?){{else}}[\s\S]*?{{\/if}}/g, '$1');
      emailTemplate = emailTemplate.replace(/{{farmLogo}}/g, farmLogo);
    } else {
      emailTemplate = emailTemplate.replace(/{{#if farmLogo}}[\s\S]*?{{else}}([\s\S]*?){{\/if}}/g, '$1');
    }

    // Send login email
    await transporter.sendMail({
      from: process.env.EMAIL_FROM || '<EMAIL>',
      to: customer.email,
      subject: `Login Link for ${farm.name} Customer Portal`,
      html: emailTemplate
    });

    return res.status(200).json({ 
      message: 'If your email or phone number is registered, you will receive a login link.' 
    });
  } catch (error) {
    console.error('Send login link error:', error);
    return res.status(500).json({ error: 'Failed to send login link. Please try again later.' });
  }
};

// Login with one-time token
export const loginWithToken = async (req, res) => {
  try {
    const { token } = req.params;

    // Find customer with this token
    const customer = await Customer.findOne({
      where: {
        login_token: token,
        login_token_expires: { [sequelize.Op.gt]: new Date() }
      }
    });

    if (!customer) {
      return res.status(400).json({ error: 'Invalid or expired login token' });
    }

    // Get farm information
    const farm = await Farm.findByPk(customer.farm_id);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Generate tokens
    const { token: authToken, refreshToken } = await generateTokens(customer.id, customer.farm_id);

    // Invalidate the one-time login token
    customer.login_token = null;
    customer.login_token_expires = null;
    await customer.save();

    // Set refresh token as HTTP-only cookie with cross-subdomain support
    res.cookie('refresh_token', refreshToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production' || true, // Always secure for cross-subdomain
      sameSite: 'none', // Required for cross-subdomain
      domain: getCookieDomain(), // Set domain for cross-subdomain access
      maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
    });

    // Return customer information and token
    return res.status(200).json({
      token: authToken,
      customer: {
        id: customer.id,
        name: customer.name,
        email: customer.email,
        farm: {
          id: farm.id,
          name: farm.name
        }
      }
    });
  } catch (error) {
    console.error('Login with token error:', error);
    return res.status(500).json({ error: 'An error occurred during login. Please try again later.' });
  }
};
